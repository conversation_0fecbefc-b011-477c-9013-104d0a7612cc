import { defineStore } from 'pinia'
import type { SpeechVoice } from '~/composables/useSpeechVoices'

export const useSpeechVoicesStore = defineStore('speechVoices', {
  state: () => ({
    voices: [] as SpeechVoice[],
    selectedVoice: null as SpeechVoice | null,
    loading: false,
    error: null as string | null,
    updatings: {} as Record<string, boolean>,
    isInitialized: false
  }),

  persist: [
    {
      pick: ['selectedVoice'],
      storage: localStorage
    }
  ],

  getters: {
    favoriteVoices: state =>
      state.voices.filter(voice => voice.is_favorite),

    voicesByGender: (state) => {
      const grouped: Record<string, SpeechVoice[]> = {}
      state.voices.forEach((voice) => {
        if (!grouped[voice.gender]) {
          grouped[voice.gender] = []
        }
        grouped[voice.gender]!.push(voice)
      })
      return grouped
    },

    premiumVoices: state => state.voices.filter(voice => voice.premium),

    freeVoices: state => state.voices.filter(voice => !voice.premium)
  },

  actions: {
    async loadVoices(forceReload = false) {
      // Skip loading if voices already exist and not forcing reload
      if (this.voices.length > 0 && !forceReload && this.isInitialized) {
        return
      }

      // Skip if already loading
      if (this.loading) {
        return
      }

      this.loading = true
      this.error = null

      try {
        const { apiService } = useAPI()
        const { getAccentByValue } = useSpeechVoices()
        const response = await apiService.get('/voice-library/all')
        this.voices = (response.data?.result || []).map((voice: any) => ({
          ...voice,
          icon: this.getVoiceIcon(voice)
        }))
        this.isInitialized = true

        // Set default voice if none selected
        if (!this.selectedVoice && this.voices.length > 0) {
          this.selectedVoice = this.voices[0]!
        }
      } catch (err: any) {
        console.error('Failed to load speech voices:', err)
        this.error = err?.message || 'Failed to load voices'
      } finally {
        this.loading = false
      }
    },

    selectVoice(voice: SpeechVoice) {
      // Ensure the voice has the correct icon before selecting
      const voiceWithIcon = {
        ...voice,
        icon: this.getVoiceIcon(voice)
      }
      this.selectedVoice = voiceWithIcon
    },

    getVoiceIcon(voice: any): string {
      // For Gemini voices, generate custom avatar SVG
      if (voice.type === 'gemini_voice') {
        return this.generateGeminiVoiceAvatar(voice)
      }

      // For other voices, use accent-based flag icons
      const { getAccentByValue } = useSpeechVoices()
      return getAccentByValue(voice.accent)?.icon || 'i-ph-question-fill'
    },

    generateGeminiVoiceAvatar(voice: any): string {
      // Generate a unique avatar based on voice characteristics
      const name = voice.speaker_name || 'Unknown'
      const gender = voice.gender || 'neutral'
      const age = voice.age || 'adult'

      // Create a unique seed based on voice properties
      const seed = this.hashString(name + gender + age + voice.id)

      // Generate avatar based on characteristics
      return this.createAvatarSVG(name, gender, age, seed)
    },

    hashString(str: string): number {
      let hash = 0
      for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i)
        hash = ((hash << 5) - hash) + char
        hash = hash & hash // Convert to 32bit integer
      }
      return Math.abs(hash)
    },

    createAvatarSVG(name: string, gender: string, age: string, seed: number): string {
      // Generate colors based on seed with better color schemes
      const colorSchemes = [
        { bg: '#6366f1', text: '#ffffff' }, // Indigo
        { bg: '#8b5cf6', text: '#ffffff' }, // Violet
        { bg: '#06b6d4', text: '#ffffff' }, // Cyan
        { bg: '#10b981', text: '#ffffff' }, // Emerald
        { bg: '#f59e0b', text: '#ffffff' }, // Amber
        { bg: '#ef4444', text: '#ffffff' }, // Red
        { bg: '#ec4899', text: '#ffffff' }, // Pink
        { bg: '#84cc16', text: '#ffffff' } // Lime
      ]

      const colorScheme = colorSchemes[seed % colorSchemes.length]!

      // Get initials from name
      const initials = name
        .split(' ')
        .map(word => word.charAt(0).toUpperCase())
        .slice(0, 2)
        .join('')

      // Generate gradient for more visual appeal
      const gradientId = `grad-${seed}`

      // Create SVG data URL with gradient background
      const svg = `
        <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <linearGradient id="${gradientId}" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" style="stop-color:${colorScheme.bg};stop-opacity:1" />
              <stop offset="100%" style="stop-color:${colorScheme.bg};stop-opacity:0.8" />
            </linearGradient>
          </defs>
          <circle cx="20" cy="20" r="20" fill="url(#${gradientId})"/>
          <circle cx="20" cy="20" r="19" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
          <text x="20" y="26" text-anchor="middle" fill="${colorScheme.text}" font-family="system-ui, -apple-system, sans-serif" font-size="13" font-weight="600">
            ${initials}
          </text>
          ${this.getGenderIcon(gender, colorScheme.text)}
          ${this.getAgeIndicator(age, colorScheme.text)}
        </svg>
      `.trim()

      return `data:image/svg+xml;base64,${btoa(svg)}`
    },

    getGenderIcon(gender: string, color: string): string {
      const x = 32
      const y = 8

      switch (gender.toLowerCase()) {
        case 'male':
          return `<circle cx="${x}" cy="${y}" r="2" fill="${color}" opacity="0.7"/>
                  <path d="M${x + 1.5} ${y - 1.5} L${x + 3} ${y - 3} M${x + 3} ${y - 3} L${x + 1.5} ${y - 3} M${x + 3} ${y - 3} L${x + 3} ${y - 1.5}" stroke="${color}" stroke-width="0.8" fill="none" opacity="0.7"/>`
        case 'female':
          return `<circle cx="${x}" cy="${y}" r="2" fill="none" stroke="${color}" stroke-width="0.8" opacity="0.7"/>
                  <path d="M${x} ${y + 2} L${x} ${y + 4} M${x - 1} ${y + 3.5} L${x + 1} ${y + 3.5}" stroke="${color}" stroke-width="0.8" opacity="0.7"/>`
        default:
          return `<circle cx="${x}" cy="${y}" r="1.5" fill="${color}" opacity="0.5"/>`
      }
    },

    getAgeIndicator(age: string, color: string): string {
      const x = 8
      const y = 32

      switch (age.toLowerCase()) {
        case 'young':
        case 'child':
          return `<circle cx="${x}" cy="${y}" r="1.5" fill="${color}" opacity="0.6"/>
                  <circle cx="${x}" cy="${y}" r="2.5" fill="none" stroke="${color}" stroke-width="0.5" opacity="0.4"/>`
        case 'middle':
        case 'adult':
          return `<rect x="${x - 1.5}" y="${y - 1.5}" width="3" height="3" fill="${color}" opacity="0.6" rx="0.5"/>`
        case 'old':
        case 'elderly':
          return `<path d="M${x - 1.5} ${y + 1.5} L${x} ${y - 1.5} L${x + 1.5} ${y + 1.5}" stroke="${color}" stroke-width="0.8" fill="none" opacity="0.6"/>`
        default:
          return ''
      }
    },

    async toggleFavorite(voiceId: string, addFavorite: boolean) {
      const voice = this.voices.find(v => v.id === voiceId)
      if (!voice) return

      this.updatings[voiceId] = true

      try {
        const { apiService } = useAPI()
        if (addFavorite) {
          await apiService.post('/voice-library/add-favorite', {
            voice_id: voiceId
          })
        } else {
          await apiService.delete('/voice-library/remove-favorite', {
            data: { voice_id: voiceId }
          })
        }

        voice.is_favorite = !voice.is_favorite
      } catch (err: any) {
        console.error('Failed to toggle favorite:', err)
      } finally {
        this.updatings[voiceId] = false
      }
    },

    // Initialize store - load voices if not loaded
    async initialize() {
      if (!this.isInitialized && this.voices.length === 0) {
        await this.loadVoices()
      }
    }
  }
})

<script setup lang="ts">
interface Props {
  beforeContent: string
  afterContent: string
  beforeLabel?: string
  afterLabel?: string
  beforeType: 'text' | 'image'
  afterType: 'video'
  initialPosition?: number
  height?: string
  width?: string
}

const props = withDefaults(defineProps<Props>(), {
  beforeLabel: 'Before',
  afterLabel: 'After',
  initialPosition: 50,
  height: '400px',
  width: '100%'
})

const sliderPosition = ref(props.initialPosition)
const isDragging = ref(false)
const containerRef = ref<HTMLElement>()
const videoRef = ref<HTMLVideoElement>()
const videoLoaded = ref(false)
const videoError = ref(false)

const handleMouseDown = (event: MouseEvent) => {
  isDragging.value = true
  updatePosition(event)
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

const handleMouseMove = (event: MouseEvent) => {
  if (isDragging.value) {
    updatePosition(event)
  }
}

const handleMouseUp = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
}

const handleTouchStart = (event: TouchEvent) => {
  isDragging.value = true
  updatePositionTouch(event)
}

const handleTouchMove = (event: TouchEvent) => {
  if (isDragging.value) {
    event.preventDefault()
    updatePositionTouch(event)
  }
}

const handleTouchEnd = () => {
  isDragging.value = false
}

const updatePosition = (event: MouseEvent) => {
  if (!containerRef.value) return

  const rect = containerRef.value.getBoundingClientRect()
  const x = event.clientX - rect.left
  const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100))
  sliderPosition.value = percentage
}

const updatePositionTouch = (event: TouchEvent) => {
  if (!containerRef.value || !event.touches[0]) return

  const rect = containerRef.value.getBoundingClientRect()
  const x = event.touches[0].clientX - rect.left
  const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100))
  sliderPosition.value = percentage
}

// Watch for position changes to sync with parent component
watch(() => props.initialPosition, (newPosition) => {
  sliderPosition.value = newPosition
})

// Video event handlers
const onVideoLoaded = () => {
  videoLoaded.value = true
  if (videoRef.value) {
    videoRef.value.play().catch(() => {
      // Ignore autoplay errors
    })
  }
}

const onVideoError = () => {
  videoError.value = true
}

// Auto-play video when mounted
onMounted(() => {
  if (videoRef.value) {
    videoRef.value.muted = true
    videoRef.value.loop = true
  }
})
</script>

<template>
  <div
    ref="containerRef"
    class="relative overflow-hidden rounded-lg cursor-ew-resize select-none"
    :style="{ height: props.height, width: props.width }"
    @mousedown="handleMouseDown"
    @touchstart="handleTouchStart"
    @touchmove="handleTouchMove"
    @touchend="handleTouchEnd"
  >
    <!-- After Content (Video - Background) -->
    <div class="absolute inset-0">
      <!-- Loading State -->
      <div
        v-if="!videoLoaded && !videoError"
        class="w-full h-full bg-gray-200 dark:bg-gray-800 flex items-center justify-center"
      >
        <div class="text-center">
          <UIcon
            name="i-lucide-loader-2"
            class="w-8 h-8 text-primary-500 animate-spin mb-2"
          />
          <p class="text-sm text-gray-600 dark:text-gray-400">Loading video...</p>
        </div>
      </div>

      <!-- Error State -->
      <div
        v-else-if="videoError"
        class="w-full h-full bg-gray-200 dark:bg-gray-800 flex items-center justify-center"
      >
        <div class="text-center">
          <UIcon
            name="i-lucide-video-off"
            class="w-8 h-8 text-gray-500 mb-2"
          />
          <p class="text-sm text-gray-600 dark:text-gray-400">Video unavailable</p>
        </div>
      </div>

      <!-- Video Content -->
      <video
        v-else-if="props.afterType === 'video'"
        ref="videoRef"
        :src="props.afterContent"
        class="w-full h-full object-cover"
        muted
        loop
        autoplay
        playsinline
        @loadeddata="onVideoLoaded"
        @error="onVideoError"
      />

      <!-- After Label -->
      <div class="absolute top-4 right-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm font-medium">
        {{ props.afterLabel }}
      </div>
    </div>

    <!-- Before Content (Text or Image - Clipped) -->
    <div
      class="absolute inset-0 overflow-hidden"
      :style="{ clipPath: `inset(0 ${100 - sliderPosition}% 0 0)` }"
    >
      <!-- Text Content -->
      <div
        v-if="props.beforeType === 'text'"
        class="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900 flex items-center justify-center p-6"
      >
        <div class="text-center max-w-md">
          <UIcon
            name="i-lucide-type"
            class="w-12 h-12 text-primary-500 mx-auto mb-4"
          />
          <p class="text-gray-800 dark:text-gray-200 text-lg font-medium leading-relaxed">
            "{{ props.beforeContent }}"
          </p>
        </div>
      </div>

      <!-- Image Content -->
      <img
        v-else-if="props.beforeType === 'image'"
        :src="props.beforeContent"
        :alt="props.beforeLabel"
        class="w-full h-full object-cover"
        draggable="false"
      >

      <!-- Before Label -->
      <div class="absolute top-4 left-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm font-medium">
        {{ props.beforeLabel }}
      </div>
    </div>

    <!-- Slider Handle -->
    <div
      class="absolute top-0 bottom-0 w-1 bg-white shadow-lg z-10 cursor-ew-resize"
      :style="{ left: `${sliderPosition}%`, transform: 'translateX(-50%)' }"
    >
      <!-- Slider Circle -->
      <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-white rounded-full shadow-lg border-2 border-primary-500 flex items-center justify-center">
        <div class="flex space-x-0.5">
          <div class="w-0.5 h-4 bg-primary-500 rounded-full" />
          <div class="w-0.5 h-4 bg-primary-500 rounded-full" />
        </div>
      </div>
    </div>

    <!-- Type Indicators -->
    <div class="absolute bottom-4 left-4 flex items-center space-x-2">
      <div class="bg-black/70 text-white px-2 py-1 rounded text-xs font-medium flex items-center space-x-1">
        <UIcon
          :name="props.beforeType === 'text' ? 'i-lucide-type' : 'i-lucide-image'"
          class="w-3 h-3"
        />
        <span>{{ props.beforeType === 'text' ? 'Text' : 'Image' }}</span>
      </div>
    </div>

    <div class="absolute bottom-4 right-4 flex items-center space-x-2">
      <div class="bg-black/70 text-white px-2 py-1 rounded text-xs font-medium flex items-center space-x-1">
        <UIcon
          name="i-lucide-video"
          class="w-3 h-3"
        />
        <span>Video</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const { t } = useI18n()
const productStore = useProductStore()
const { getServicePriceByModelName, oneUSDCredits } = storeToRefs(productStore)
const creditsStore = useCreditsStore()

// Pricing Calculator state
const calculatorMode = ref('budget') // budget | resources
const budgetAmount = ref(10) // minimum $10
const resourceCounts = ref<Record<string, number>>({
  'imagen-flash': 0,
  'imagen-4-fast': 0,
  'imagen-4': 0,
  'imagen-4-ultra': 0,
  'veo-2': 0,
  'veo-3-fast': 0,
  'veo-3': 0,
  'tts-flash': 0,
  'tts-pro': 0,
  'dialogue-flash': 0,
  'dialogue-pro': 0
})

// Calculator mode items
const calculatorModeItems = computed(() => {
  return [
    {
      label: t('Budget Calculator'),
      value: 'budget',
      icon: 'solar:dollar-bold'
    },
    {
      label: t('Resource Calculator'),
      value: 'resources',
      icon: 'solar:gallery-bold'
    }
  ]
})

// Service pricing data
const servicePricing = computed(() => {
  return [
    {
      id: 'imagen-flash',
      name: t('Gemini 2.0 Flash'),
      type: 'image',
      icon: 'i-lucide-zap',
      iconColor: 'text-yellow-500',
      priceUnit: 0.039,
      discount: 0.5,
      credits:
        getServicePriceByModelName.value('imagen-flash')?.effective_price || 20
    },
    {
      id: 'imagen-4-fast',
      name: t('Imagen 4 Fast'),
      type: 'image',
      icon: 'i-lucide-image',
      iconColor: 'text-blue-500',
      priceUnit: 0.02,
      discount: 0.5,
      credits:
        getServicePriceByModelName.value('imagen-4-fast')?.effective_price
        || 10
    },
    {
      id: 'imagen-4',
      name: t('Imagen 4'),
      type: 'image',
      icon: 'i-lucide-image-plus',
      iconColor: 'text-green-500',
      priceUnit: 0.04,
      discount: 0.5,
      credits:
        getServicePriceByModelName.value('imagen-4')?.effective_price || 20
    },
    {
      id: 'imagen-4-ultra',
      name: t('Imagen 4 Ultra'),
      type: 'image',
      icon: 'i-lucide-crown',
      iconColor: 'text-purple-500',
      priceUnit: 0.06,
      discount: 0.5,
      credits:
        getServicePriceByModelName.value('imagen-4-ultra')?.effective_price
        || 30
    },
    {
      id: 'veo-2',
      name: t('Veo 2'),
      type: 'video',
      icon: 'i-lucide-video',
      iconColor: 'text-red-500',
      priceUnit: 0.5,
      discount: 0.5,
      credits:
        getServicePriceByModelName.value('veo-2')?.effective_price || 250
    },
    {
      id: 'veo-3-fast',
      name: t('Veo 3 Fast'),
      type: 'video',
      icon: 'i-lucide-play-circle',
      iconColor: 'text-orange-500',
      priceUnit: 0.5,
      discount: 0.5,
      credits:
        getServicePriceByModelName.value('veo-3-fast')?.effective_price || 250
    },
    {
      id: 'veo-3',
      name: t('Veo 3'),
      type: 'video',
      icon: 'i-lucide-film',
      iconColor: 'text-indigo-500',
      priceUnit: 0.75,
      discount: 0.5,
      credits:
        getServicePriceByModelName.value('veo-3')?.effective_price || 375
    },
    {
      id: 'tts-flash',
      name: t('Gemini 2.5 Flash TTS'),
      type: 'speech',
      icon: 'i-lucide-mic',
      iconColor: 'text-emerald-500',
      priceUnit: 0.02, // $20 per 1M characters
      discount: 1, // No discount
      credits:
        getServicePriceByModelName.value('tts-flash')?.effective_price || 1
    },
    {
      id: 'tts-pro',
      name: t('Gemini 2.5 Pro TTS'),
      type: 'speech',
      icon: 'i-lucide-mic-2',
      iconColor: 'text-teal-500',
      priceUnit: 0.04, // $40 per 1M characters
      discount: 1, // No discount
      credits:
        getServicePriceByModelName.value('tts-pro')?.effective_price || 2
    },
    {
      id: 'dialogue-flash',
      name: t('Gemini 2.5 Flash Dialogue'),
      type: 'dialogue',
      icon: 'i-lucide-users',
      iconColor: 'text-purple-500',
      priceUnit: 0.02, // Same as TTS Flash but for multi-speaker
      discount: 1, // No discount
      credits:
        getServicePriceByModelName.value('tts-flash')?.effective_price || 1
    },
    {
      id: 'dialogue-pro',
      name: t('Gemini 2.5 Pro Dialogue'),
      type: 'dialogue',
      icon: 'i-lucide-message-circle',
      iconColor: 'text-pink-500',
      priceUnit: 0.04, // Same as TTS Pro but for multi-speaker
      discount: 1, // No discount
      credits:
        getServicePriceByModelName.value('tts-pro')?.effective_price || 2
    }
  ]
})

// Calculate resources from budget
const calculatedResources = computed(() => {
  const budget = budgetAmount.value
  const totalCredits = budget * oneUSDCredits.value

  return servicePricing.value.map((service) => {
    const maxResources = Math.floor(totalCredits / service.credits)
    return {
      ...service,
      maxResources,
      actualPrice: service.priceUnit * service.discount
    }
  })
})

// Calculate total price from resources
const calculatedPrice = computed(() => {
  let totalCredits = 0

  servicePricing.value.forEach((service) => {
    const count = resourceCounts.value[service.id] || 0
    totalCredits += count * service.credits
  })

  return totalCredits / oneUSDCredits.value
})

// Check if purchase amount meets minimum requirement
const canPurchase = computed(() => {
  if (calculatorMode.value === 'budget') {
    return budgetAmount.value >= 10
  } else {
    return calculatedPrice.value >= 10
  }
})

// Get the actual purchase amount
const purchaseAmount = computed(() => {
  if (calculatorMode.value === 'budget') {
    return budgetAmount.value
  } else {
    return calculatedPrice.value
  }
})

// Functions
const handleBuyNow = () => {
  let creditsNeeded = 0
  let totalPrice = 0

  if (calculatorMode.value === 'budget') {
    totalPrice = budgetAmount.value
    creditsNeeded = budgetAmount.value * oneUSDCredits.value
  } else {
    totalPrice = calculatedPrice.value
    servicePricing.value.forEach((service) => {
      const count = resourceCounts.value[service.id] || 0
      creditsNeeded += count * service.credits
    })
  }

  // Check minimum purchase amount
  if (totalPrice < 10) {
    const toast = useToast()
    toast.add({
      id: 'minimum-purchase-error',
      title: t('Minimum Purchase Required'),
      description: t(
        'Minimum purchase amount is $10. Please increase your selection.'
      ),
      color: 'error'
    })
    return
  }

  if (creditsNeeded > 0) {
    creditsStore.processBuyCredits(creditsNeeded)
  }
}

const resetResourceCounts = () => {
  Object.keys(resourceCounts.value).forEach((key) => {
    resourceCounts.value[key] = 0
  })
}

// Helper function to format duration
const formatDuration = (seconds: number) => {
  if (seconds < 60) {
    return `${seconds}s`
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`
  } else {
    const hours = Math.floor(seconds / 3600)
    const remainingMinutes = Math.floor((seconds % 3600) / 60)
    const remainingSeconds = seconds % 60
    let result = `${hours}h`
    if (remainingMinutes > 0) result += ` ${remainingMinutes}m`
    if (remainingSeconds > 0) result += ` ${remainingSeconds}s`
    return result
  }
}

// Get quick values for sliders
const getQuickValues = (type: string) => {
  if (type === 'image') {
    return [
      { value: 100, label: '100' },
      { value: 1000, label: '1K' },
      { value: 10000, label: '10K' },
      { value: 50000, label: '50K' },
      { value: 100000, label: '100K' }
    ]
  } else if (type === 'video') {
    // Video quick values in seconds with time labels
    return [
      { value: 30, label: '30s' },
      { value: 60, label: '1m' },
      { value: 300, label: '5m' },
      { value: 600, label: '10m' },
      { value: 1800, label: '30m' },
      { value: 3600, label: '1h' }
    ]
  } else if (type === 'speech') {
    // Speech quick values in characters
    return [
      { value: 1000, label: '1K' },
      { value: 5000, label: '5K' },
      { value: 10000, label: '10K' },
      { value: 50000, label: '50K' },
      { value: 100000, label: '100K' },
      { value: 1000000, label: '1M' }
    ]
  } else if (type === 'dialogue') {
    // Dialogue quick values in characters (similar to speech but focused on conversation length)
    return [
      { value: 500, label: '500' },
      { value: 2000, label: '2K' },
      { value: 5000, label: '5K' },
      { value: 10000, label: '10K' },
      { value: 25000, label: '25K' },
      { value: 50000, label: '50K' }
    ]
  } else {
    return []
  }
}
</script>

<template>
  <div>
    <div class="mb-4">
      <h2 class="text-xl font-bold">
        {{ $t("Pricing Calculator") }}
      </h2>
      <p class="text-sm text-muted">
        {{
          $t("Calculate how many resources can you generate with your budget.")
        }}
      </p>
    </div>
    <UPricingPlan
      :price="
        calculatorMode === 'budget'
          ? `$${budgetAmount}`
          : `$${calculatedPrice.toFixed(2)}`
      "
      :button="{
        label: canPurchase ? $t('Buy now') : $t('Minimum $10 required'),
        onClick: handleBuyNow,
        disabled: !canPurchase,
        color: canPurchase ? 'primary' : 'neutral'
      }"
      orientation="horizontal"
      variant="outline"
    >
      <template #body>
        <div class="space-y-6 w-full flex-1">
          <!-- Calculator Mode Toggle -->
          <div class="flex justify-center">
            <UTabs
              v-model="calculatorMode"
              color="primary"
              :content="false"
              :items="calculatorModeItems"
              class="w-fit"
              size="sm"
            />
          </div>

          <!-- Budget Calculator Mode -->
          <div
            v-if="calculatorMode === 'budget'"
            class="space-y-4"
          >
            <UFormField
              :label="$t('Budget Amount')"
              class="w-full"
            >
              <div class="space-y-3">
                <!-- Budget Input Field -->
                <div class="flex items-center gap-3">
                  <div class="flex-1">
                    <UInputNumber
                      v-model="budgetAmount"
                      :min="10"
                      :max="100000"
                      :step="1"
                      size="lg"
                      class="w-full"
                      :placeholder="$t('Enter budget amount')"
                    >
                      <template #leading>
                        <span class="text-gray-500">$</span>
                      </template>
                    </UInputNumber>
                  </div>
                  <div class="text-sm text-gray-500">
                    {{ $t("Min: $10") }}
                  </div>
                </div>

                <!-- Slider -->
                <USlider
                  v-model="budgetAmount"
                  :min="10"
                  :max="10000"
                  :step="5"
                  class="w-full"
                />

                <!-- Quick Budget Values -->
                <div
                  class="flex justify-between items-center text-sm text-gray-500"
                >
                  <span>$10</span>
                  <div class="flex gap-2">
                    <button
                      v-for="quickBudget in [50, 100, 500, 1000, 5000]"
                      :key="quickBudget"
                      class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                      @click="budgetAmount = quickBudget"
                    >
                      ${{ formatNumber(quickBudget) }}
                    </button>
                  </div>
                  <span>$10,000</span>
                </div>
              </div>
            </UFormField>

            <!-- Resources Display -->
            <div class="space-y-3">
              <div>
                <h4 class="font-semibold text-sm">
                  {{ $t("Resources you can generate:") }}
                </h4>
                <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">
                  {{
                    $t(
                      "Each amount shows what you can generate with your entire budget (choose one type). Video duration is measured in seconds, speech and dialogue generation in characters."
                    )
                  }}
                </p>
              </div>
              <div class="space-y-3">
                <div
                  v-for="(resource, index) in calculatedResources"
                  :key="resource.id"
                  class="relative"
                >
                  <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
                    <div class="flex justify-between items-center">
                      <div class="flex items-center gap-3">
                        <div class="flex-shrink-0">
                          <UIcon
                            :name="resource.icon"
                            :class="['size-5', resource.iconColor]"
                          />
                        </div>
                        <div>
                          <div class="font-medium text-sm">
                            {{ resource.name }}
                          </div>
                          <div class="text-xs text-gray-500">
                            ${{ resource.actualPrice }}
                          </div>
                        </div>
                      </div>
                      <div class="text-right">
                        <div class="font-bold text-primary">
                          {{ formatNumber(resource.maxResources) }}
                        </div>
                        <div class="text-xs text-gray-500">
                          {{
                            resource.type === "image"
                              ? $t("Images")
                              : resource.type === "video"
                                ? $t("Seconds")
                                : resource.type === "speech"
                                  ? $t("Characters")
                                  : $t("Characters")
                          }}
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- OR separator (except for last item) -->
                  <div
                    v-if="index < calculatedResources.length - 1"
                    class="flex justify-center items-center my-2"
                  >
                    <div class="flex items-center gap-2 text-xs text-gray-500">
                      <div class="h-px bg-gray-300 dark:bg-gray-600 flex-1" />
                      <span
                        class="px-2 bg-white dark:bg-gray-900 font-medium"
                      >{{ $t("OR") }}</span>
                      <div class="h-px bg-gray-300 dark:bg-gray-600 flex-1" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Resource Calculator Mode -->
          <div
            v-else
            class="space-y-4"
          >
            <div class="space-y-2">
              <div class="flex justify-between items-center">
                <h4 class="font-semibold text-sm">
                  {{ $t("Select resources you want:") }}
                </h4>
                <UButton
                  size="xs"
                  variant="ghost"
                  color="neutral"
                  @click="resetResourceCounts"
                >
                  {{ $t("Reset") }}
                </UButton>
              </div>
              <p class="text-xs text-gray-600 dark:text-gray-400">
                {{ $t("For video generation, enter duration in seconds. For speech and dialogue generation, enter number of characters. Use quick buttons for common values.") }}
              </p>
            </div>

            <div class="space-y-4">
              <div
                v-for="service in servicePricing"
                :key="service.id"
                class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4"
              >
                <div class="flex justify-between items-center mb-2">
                  <div class="flex items-center gap-3">
                    <div class="flex-shrink-0">
                      <UIcon
                        :name="service.icon"
                        :class="['size-5', service.iconColor]"
                      />
                    </div>
                    <div>
                      <div class="font-medium text-sm">
                        {{ service.name }}
                      </div>
                      <div class="text-xs text-gray-500">
                        {{ service.credits }} {{ $t("credits") }} /
                        {{
                          service.type === "image"
                            ? $t("image")
                            : service.type === "video"
                              ? $t("second")
                              : service.type === "speech"
                                ? $t("character")
                                : $t("character")
                        }}
                      </div>
                    </div>
                  </div>
                  <div class="text-right">
                    <div class="font-bold text-primary">
                      ${{ (service.priceUnit * service.discount).toFixed(3) }}
                    </div>
                    <div class="text-xs text-gray-500">
                      {{
                        service.type === "image"
                          ? $t("per image")
                          : service.type === "video"
                            ? $t("per second")
                            : service.type === "speech"
                              ? $t("per character")
                              : $t("per character")
                      }}
                    </div>
                  </div>
                </div>

                <UFormField
                  :label="service.type === 'image'
                    ? `${$t('Quantity')}: ${formatNumber(resourceCounts[service.id] || 0)}`
                    : service.type === 'video'
                      ? `${$t('Duration')}: ${formatDuration(resourceCounts[service.id] || 0)} (${formatNumber(resourceCounts[service.id] || 0)} ${$t('seconds')})`
                      : service.type === 'speech'
                        ? `${$t('Characters')}: ${formatNumber(resourceCounts[service.id] || 0)}`
                        : `${$t('Dialogue Characters')}: ${formatNumber(resourceCounts[service.id] || 0)}`"
                  class="w-full"
                >
                  <div class="space-y-3">
                    <!-- Slider -->
                    <USlider
                      v-model="resourceCounts[service.id]"
                      :min="0"
                      :max="1000000"
                      :step="1"
                      class="w-full"
                    />

                    <!-- Quick values -->
                    <div class="flex justify-between text-xs text-gray-500">
                      <span>{{
                        service.type === 'image'
                          ? '0'
                          : service.type === 'video'
                            ? '0s'
                            : service.type === 'speech'
                              ? '0 chars'
                              : '0 chars'
                      }}</span>
                      <div class="flex gap-2">
                        <button
                          v-for="quickValue in getQuickValues(service.type)"
                          :key="typeof quickValue === 'object' ? quickValue.value : quickValue"
                          class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                          @click="resourceCounts[service.id] = typeof quickValue === 'object' ? quickValue.value : quickValue"
                        >
                          {{ typeof quickValue === 'object' ? quickValue.label : formatNumber(quickValue) }}
                        </button>
                      </div>
                      <span>{{
                        service.type === 'image'
                          ? formatNumber(1000000)
                          : service.type === 'video'
                            ? '278h'
                            : service.type === 'speech'
                              ? '1M chars'
                              : '50K chars'
                      }}</span>
                    </div>

                    <!-- Input Number for precise control -->
                    <UInputNumber
                      v-model="resourceCounts[service.id]"
                      :min="0"
                      :max="1000000"
                      :step="1"
                      size="sm"
                      class="w-full"
                      :placeholder="service.type === 'image'
                        ? $t('Enter exact number')
                        : service.type === 'video'
                          ? $t('Enter duration in seconds')
                          : service.type === 'speech'
                            ? $t('Enter number of characters')
                            : $t('Enter dialogue characters')"
                    />
                  </div>
                </UFormField>
              </div>
            </div>

            <!-- Total Display -->
            <div class="bg-primary-50 dark:bg-primary-900/20 rounded-lg p-4">
              <div class="flex justify-between items-center">
                <span class="font-semibold">{{ $t("Total Cost:") }}</span>
                <span class="text-xl font-bold text-primary">${{ calculatedPrice.toFixed(2) }}</span>
              </div>
              <div class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {{
                  $t("Approximately {credits} credits", {
                    credits: formatNumber(Math.round(calculatedPrice * oneUSDCredits))
                  })
                }}
              </div>

              <!-- Minimum Purchase Warning -->
              <div
                v-if="!canPurchase && calculatedPrice > 0"
                class="mt-3 p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg"
              >
                <div class="flex items-center gap-2">
                  <UIcon
                    name="i-lucide-alert-triangle"
                    class="size-4 text-amber-600 dark:text-amber-400"
                  />
                  <span
                    class="text-sm text-amber-800 dark:text-amber-200 font-medium"
                  >
                    {{ $t("Minimum purchase amount is $10") }}
                  </span>
                </div>
                <div class="text-xs text-amber-700 dark:text-amber-300 mt-1">
                  {{
                    $t(
                      "Please add more resources to reach the minimum purchase amount."
                    )
                  }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </UPricingPlan>
  </div>
</template>
